<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.nodal</groupId>
        <artifactId>nodal</artifactId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>

    <artifactId>nodal-server</artifactId>
    <packaging>jar</packaging>

    <name>${project.artifactId}</name>
    <description>
        后端 Server 的主项目，通过引入需要 nodal-module-xxx 的依赖，
        从而实现提供 RESTful API 给 nodal-ui-admin、nodal-ui-user 等前端项目。
        本质上来说，它就是个空壳（容器）！
    </description>
    <url>https://gitlab.nodal.work/nodal-base/yudao2024/nodal-api</url>

    <dependencies>
        <dependency>
            <groupId>com.nodal</groupId>
            <artifactId>nodal-module-system-biz</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>com.nodal</groupId>
            <artifactId>nodal-module-infra-biz</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>com.nodal</groupId>
            <artifactId>nodal-module-jishu-biz</artifactId>
            <version>${revision}</version>
        </dependency>

        <!-- 会员中心。默认注释，保证编译速度 -->
<!--        <dependency>-->
<!--            <groupId>com.nodal</groupId>-->
<!--            <artifactId>nodal-module-member-biz</artifactId>-->
<!--            <version>${revision}</version>-->
<!--        </dependency>-->

        <!-- 数据报表。默认注释，保证编译速度 -->
<!--        <dependency>-->
<!--            <groupId>com.nodal</groupId>-->
<!--            <artifactId>nodal-module-report-biz</artifactId>-->
<!--            <version>${revision}</version>-->
<!--        </dependency>-->
        <!-- 工作流。默认注释，保证编译速度 -->
<!--        <dependency>-->
<!--            <groupId>com.nodal</groupId>-->
<!--            <artifactId>nodal-module-bpm-biz</artifactId>-->
<!--            <version>${revision}</version>-->
<!--        </dependency>-->
        <!-- 支付服务。默认注释，保证编译速度 -->
<!--        <dependency>-->
<!--            <groupId>com.nodal</groupId>-->
<!--            <artifactId>nodal-module-pay-biz</artifactId>-->
<!--            <version>${revision}</version>-->
<!--        </dependency>-->

        <!-- 微信公众号模块。默认注释，保证编译速度 -->
<!--        <dependency>-->
<!--            <groupId>com.nodal</groupId>-->
<!--            <artifactId>nodal-module-mp-biz</artifactId>-->
<!--            <version>${revision}</version>-->
<!--        </dependency>-->

        <!-- 商城相关模块。默认注释，保证编译速度-->
<!--        <dependency>-->
<!--            <groupId>com.nodal</groupId>-->
<!--            <artifactId>nodal-module-promotion-biz</artifactId>-->
<!--            <version>${revision}</version>-->
<!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>com.nodal</groupId>-->
<!--            <artifactId>nodal-module-product-biz</artifactId>-->
<!--            <version>${revision}</version>-->
<!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>com.nodal</groupId>-->
<!--            <artifactId>nodal-module-trade-biz</artifactId>-->
<!--            <version>${revision}</version>-->
<!--        </dependency>-->
<!--        <dependency>-->
<!--            <groupId>com.nodal</groupId>-->
<!--            <artifactId>nodal-module-statistics-biz</artifactId>-->
<!--            <version>${revision}</version>-->
<!--        </dependency>-->

        <!-- CRM 相关模块。默认注释，保证编译速度 -->
<!--        <dependency>-->
<!--            <groupId>com.nodal</groupId>-->
<!--            <artifactId>nodal-module-crm-biz</artifactId>-->
<!--            <version>${revision}</version>-->
<!--        </dependency>-->

        <!-- ERP 相关模块。默认注释，保证编译速度 -->
<!--        <dependency>-->
<!--            <groupId>com.nodal</groupId>-->
<!--            <artifactId>nodal-module-erp-biz</artifactId>-->
<!--            <version>${revision}</version>-->
<!--        </dependency>-->

        <!-- AI 大模型相关模块。默认注释，保证编译速度 -->
<!--        <dependency>-->
<!--            <groupId>com.nodal</groupId>-->
<!--            <artifactId>nodal-module-ai-biz</artifactId>-->
<!--            <version>${revision}</version>-->
<!--        </dependency>-->

        <!-- spring boot 配置所需依赖 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <optional>true</optional>
        </dependency>

        <!-- 服务保障相关 -->
        <dependency>
            <groupId>com.nodal</groupId>
            <artifactId>nodal-spring-boot-starter-protection</artifactId>
        </dependency>

    </dependencies>

    <build>
        <!-- 设置构建的 jar 包名 -->
        <finalName>${project.artifactId}</finalName>
        <resources>
            <!--排除配置文件-->
            <resource>
                <directory>src/main/resources</directory>
                <excludes>
                    <exclude>smart-doc.json</exclude>
                    <exclude>*/**.yml</exclude>
                    <exclude>*.yml</exclude>
                    <exclude>*.xml</exclude>
                    <exclude>rebel.xml</exclude>
                </excludes>
                <filtering>true</filtering>

            </resource>
            <!--根据环境配置打包配置文件-->
            <resource>
                <directory>src/main/resources</directory>
                <!--引入所需的配置文件-->
                <filtering>true</filtering>
                <includes>
                    <include>application.yml</include>
                    <include>application-${environment}.yml</include>
                    <include>application-${environment}.properties</include>
                    <include>logback-spring.xml</include>
                </includes>
            </resource>
        </resources>
        <plugins>
            <!-- 打包 -->
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
                <version>${spring.boot.version}</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>repackage</goal> <!-- 将引入的 jar 打入其中 -->
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>com.ly.smart-doc</groupId>
                <artifactId>smart-doc-maven-plugin</artifactId>
                <version>3.0.6</version>
                <configuration>
                    <!--指定生成文档的使用的配置文件,配置文件放在自己的项目中-->
                    <configFile>./src/main/resources/smart-doc.json</configFile>
                    <!--指定项目名称，推荐使用动态参数，例如${project.description}-->
                    <!--如果smart-doc.json中和此处都未设置projectName，2.3.4开始，插件自动采用pom中的projectName作为设置-->
                    <!--<projectName>${project.description}</projectName>-->
                    <!--smart-doc实现自动分析依赖树加载第三方依赖的源码，如果一些框架依赖库加载不到导致报错，这时请使用excludes排除掉-->
                    <excludes>
                        <!--格式为：groupId:artifactId;参考如下-->
                        <!--也可以支持正则式如：com.alibaba:.* -->
                        <exclude>com.alibaba:fastjson</exclude>
                    </excludes>
                    <!--includes配置用于配置加载外部依赖源码,配置后插件会按照配置项加载外部源代码而不是自动加载所有，因此使用时需要注意-->
                    <!--smart-doc能自动分析依赖树加载所有依赖源码，原则上会影响文档构建效率，因此你可以使用includes来让插件加载你配置的组件-->
                    <includes>
                        <!--格式为：groupId:artifactId;参考如下-->
                        <!--也可以支持正则式如：com.alibaba:.* -->
                        <include>com.alibaba:fastjson</include>
                    </includes>
                </configuration>
                <executions>
                    <execution>
                        <!--如果不需要在执行编译时启动smart-doc，则将phase注释掉-->
                        <!--                        <phase>compile</phase>-->
                        <goals>
                            <!--smart-doc提供了html、openapi、markdown等goal，可按需配置-->
                            <goal>html</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
    <profiles>
        <!--本地环境-->
        <profile>
            <id>local</id>
            <activation>
                <activeByDefault>false</activeByDefault>
            </activation>
            <properties>
                <environment>local</environment>
                <jar.filename>ffxg-api</jar.filename>
            </properties>
        </profile>
        <!--开发环境-->
        <profile>
            <id>dev</id>
            <activation>
                <activeByDefault>false</activeByDefault>
            </activation>
            <properties>
                <environment>dev</environment>
                <jar.filename>ffxg-api</jar.filename>
            </properties>
        </profile>
        <!--生产环境-->
        <profile>
            <id>prod</id>
            <activation>
                <activeByDefault>false</activeByDefault>
            </activation>
            <properties>
                <environment>prod</environment>
                <jar.filename>ffxg-api</jar.filename>
            </properties>
        </profile>

    </profiles>
</project>
