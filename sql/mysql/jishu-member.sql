-- 用户中心表
CREATE TABLE `jishu_member` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '用户姓名',
  `user_type` tinyint NOT NULL COMMENT '用户类型（1调度 2司机）',
  `mobile` varchar(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '手机号码',
  `id_card` varchar(18) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '身份证件号',
  `auth_status` tinyint NOT NULL DEFAULT 0 COMMENT '认证状态（0未认证 1已认证 2认证失败）',
  `system_user_id` bigint NULL DEFAULT NULL COMMENT '关联的系统用户ID',
  `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT 0 COMMENT '租户编号',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_mobile`(`mobile` ASC, `tenant_id` ASC) USING BTREE,
  INDEX `idx_user_type`(`user_type` ASC) USING BTREE,
  INDEX `idx_auth_status`(`auth_status` ASC) USING BTREE,
  INDEX `idx_system_user_id`(`system_user_id` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '用户中心表';

-- 插入字典类型
INSERT INTO system_dict_type (name, type, status, remark) VALUES 
('用户类型', 'jishu_member_user_type', 0, '技术平台用户类型'),
('认证状态', 'jishu_member_auth_status', 0, '技术平台认证状态');

-- 插入字典数据
INSERT INTO system_dict_data (dict_type, label, value, sort, status, remark) VALUES 
('jishu_member_user_type', '调度', '1', 1, 0, '调度管理员'),
('jishu_member_user_type', '司机', '2', 2, 0, '司机'),
('jishu_member_auth_status', '未认证', '0', 1, 0, '未认证'),
('jishu_member_auth_status', '已认证', '1', 2, 0, '已认证'),
('jishu_member_auth_status', '认证失败', '2', 3, 0, '认证失败');
