# 数据库设计规范文档

## 数据库支持

### 支持的数据库类型
1. **MySQL** - 主要数据库，版本8.0+
2. **达梦数据库** - 国产数据库，版本8.1.3.62
3. **人大金仓** - 国产数据库，版本8.6.0
4. **OpenGauss** - 华为开源数据库，版本5.0.2
5. **PostgreSQL** - 开源关系型数据库
6. **Oracle** - 企业级数据库
7. **SQL Server** - 微软数据库
8. **DB2** - IBM数据库

## 表命名规范

### 1. 表名规范
- **格式**: `{模块}_{业务对象}`
- **字符集**: 小写字母、数字、下划线
- **示例**:
  - `system_user` - 系统用户表
  - `system_role` - 系统角色表
  - `infra_config` - 基础设施配置表
  - `system_menu` - 系统菜单表

### 2. 模块前缀规范
- `system_` - 系统管理模块
- `infra_` - 基础设施模块
- `bpm_` - 工作流模块
- `pay_` - 支付模块
- `mall_` - 商城模块
- `member_` - 会员模块
- `crm_` - 客户关系管理模块
- `erp_` - 企业资源规划模块
- `mp_` - 微信公众号模块
- `ai_` - AI模块

## 字段设计规范

### 1. 主键设计
```sql
`id` bigint NOT NULL AUTO_INCREMENT COMMENT '编号'
```
- **类型**: `bigint`
- **约束**: `NOT NULL AUTO_INCREMENT`
- **注释**: 统一使用'编号'或具体业务含义

### 2. 公共字段规范

#### 审计字段（必须）
```sql
`creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '创建者',
`create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
`updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '更新者',
`update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
```

#### 逻辑删除字段（必须）
```sql
`deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除'
```

#### 多租户字段（可选）
```sql
`tenant_id` bigint NOT NULL DEFAULT 0 COMMENT '租户编号'
```

### 3. 字段类型规范

#### 字符串类型
- **短文本**: `varchar(64)` - 用于名称、编码等
- **中文本**: `varchar(255)` - 用于描述、备注等
- **长文本**: `varchar(500)` - 用于详细描述
- **超长文本**: `text` - 用于内容存储

#### 数值类型
- **小整数**: `tinyint` - 状态、类型等枚举值
- **整数**: `int` - 一般数值
- **长整数**: `bigint` - ID、时间戳等

#### 时间类型
- **日期时间**: `datetime` - 标准时间格式
- **时间戳**: `bigint` - 毫秒时间戳

#### 布尔类型
- **布尔值**: `bit(1)` - 是否类型字段

### 4. 字符集和排序规则
```sql
CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci
```
- 统一使用 `utf8mb4` 字符集
- 使用 `utf8mb4_unicode_ci` 排序规则

## 索引设计规范

### 1. 主键索引
```sql
PRIMARY KEY (`id`) USING BTREE
```

### 2. 普通索引
```sql
INDEX `idx_create_time`(`create_time` ASC) USING BTREE
```

### 3. 唯一索引
```sql
UNIQUE INDEX `uk_username`(`username` ASC) USING BTREE
```

### 4. 复合索引
```sql
INDEX `idx_tenant_status`(`tenant_id` ASC, `status` ASC) USING BTREE
```

## 表结构示例

### 标准业务表结构
```sql
CREATE TABLE `system_user` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `username` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '用户账号',
  `password` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '' COMMENT '密码',
  `nickname` varchar(30) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '用户昵称',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '备注',
  `dept_id` bigint NULL DEFAULT NULL COMMENT '部门ID',
  `post_ids` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '岗位编号数组',
  `email` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '用户邮箱',
  `mobile` varchar(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '手机号码',
  `sex` tinyint NULL DEFAULT 0 COMMENT '用户性别',
  `avatar` varchar(512) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '头像地址',
  `status` tinyint NOT NULL DEFAULT 0 COMMENT '帐号状态（0正常 1停用）',
  `login_ip` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '最后登录IP',
  `login_date` datetime NULL DEFAULT NULL COMMENT '最后登录时间',
  `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT 0 COMMENT '租户编号',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_username`(`username` ASC, `update_time` ASC, `tenant_id` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '用户信息表';
```

## 数据库配置规范

### 1. 连接池配置
```yaml
spring:
  datasource:
    druid:
      initial-size: 5
      min-idle: 10
      max-active: 20
      max-wait: 600000
      time-between-eviction-runs-millis: 60000
      min-evictable-idle-time-millis: 300000
      max-evictable-idle-time-millis: 900000
```

### 2. MyBatis Plus配置
```yaml
mybatis-plus:
  configuration:
    map-underscore-to-camel-case: true
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
  global-config:
    db-config:
      id-type: NONE # 智能模式，自动适配数据库类型
      logic-delete-value: 1
      logic-not-delete-value: 0
```

## 多租户设计

### 1. 租户隔离策略
- **数据隔离**: 通过 `tenant_id` 字段实现数据隔离
- **权限隔离**: 基于租户的权限控制
- **配置隔离**: 租户级别的配置管理

### 2. 忽略租户的表
```yaml
nodal:
  tenant:
    ignore-tables:
      - system_tenant
      - system_tenant_package
      - system_dict_data
      - system_dict_type
      - system_error_code
      - system_menu
      - infra_config
```

## 数据迁移和版本控制

### 1. SQL脚本组织
```
sql/
├── mysql/                    # MySQL脚本
│   ├── ruoyi-vue-pro.sql    # 主要建表脚本
│   ├── bpm-2024-03-24.sql   # BPM模块脚本
│   ├── crm-2024-02-26.sql   # CRM模块脚本
│   └── ...
├── postgresql/              # PostgreSQL脚本
├── oracle/                  # Oracle脚本
└── tools/                   # 转换工具
```

### 2. 版本管理规范
- **文件命名**: `{模块}-{日期}.sql`
- **增量更新**: 提供增量SQL脚本
- **回滚脚本**: 重要变更提供回滚脚本

## 性能优化规范

### 1. 索引优化
- **查询频繁字段**: 创建单列索引
- **组合查询**: 创建复合索引
- **外键字段**: 创建索引提升关联查询性能

### 2. 分页查询优化
- 使用 MyBatis Plus 的分页插件
- 避免深度分页，使用游标分页

### 3. 大表优化
- **分表策略**: 按时间或业务维度分表
- **归档策略**: 历史数据归档
- **读写分离**: 配置主从数据源

## 数据安全规范

### 1. 敏感数据加密
```yaml
mybatis-plus:
  encryptor:
    password: XDV71a+xqStEA3WH # 加解密秘钥
```

### 2. 数据脱敏
- 手机号脱敏: 138****1234
- 邮箱脱敏: t***@example.com
- 身份证脱敏: 110***********1234

### 3. 审计日志
- 记录数据变更操作
- 记录操作人员和时间
- 支持数据变更追溯
