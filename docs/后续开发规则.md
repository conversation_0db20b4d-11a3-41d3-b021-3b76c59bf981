# 后续开发规则文档

## 数据库表设计规范

### 1. 表命名统一规范

#### 新业务表统一前缀
所有新开发的业务表必须以 `jishu_` 作为前缀，格式为：
```
jishu_{模块}_{业务对象}
```

#### 模块划分规范
```
jishu_user_*        # 用户相关业务
jishu_order_*       # 订单管理
jishu_product_*     # 产品管理
jishu_finance_*     # 财务管理
jishu_workflow_*    # 工作流业务
jishu_report_*      # 报表统计
jishu_message_*     # 消息通知
jishu_file_*        # 文件管理
jishu_log_*         # 业务日志
jishu_config_*      # 业务配置
jishu_wechat_*      # 微信集成
jishu_alipay_*      # 支付宝集成
jishu_sms_*         # 短信服务
jishu_email_*       # 邮件服务
```

### 2. 表设计示例

#### 用户业务表示例
```sql
-- 用户扩展信息表
CREATE TABLE `jishu_user_profile` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `real_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '真实姓名',
  `id_card` varchar(18) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '身份证号',
  `address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '详细地址',
  `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT 0 COMMENT '租户编号',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_user_id`(`user_id` ASC) USING BTREE,
  INDEX `idx_tenant_id`(`tenant_id` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '用户扩展信息表';

-- 用户积分表
CREATE TABLE `jishu_user_points` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `total_points` int NOT NULL DEFAULT 0 COMMENT '总积分',
  `available_points` int NOT NULL DEFAULT 0 COMMENT '可用积分',
  `frozen_points` int NOT NULL DEFAULT 0 COMMENT '冻结积分',
  `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT 0 COMMENT '租户编号',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_user_tenant`(`user_id` ASC, `tenant_id` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '用户积分表';
```

#### 基于角色的业务表示例

##### 人事模块表
```sql
-- 员工信息表
CREATE TABLE `jishu_hr_employee` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '员工ID',
  `employee_no` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '员工编号',
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '员工姓名',
  `phone` varchar(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '手机号码',
  `id_card` varchar(18) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '身份证号',
  `department_id` bigint NULL DEFAULT NULL COMMENT '部门ID',
  `position` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '职位',
  `entry_date` date NOT NULL COMMENT '入职日期',
  `status` tinyint NOT NULL DEFAULT 0 COMMENT '员工状态（0正常 1离职）',
  `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT 0 COMMENT '租户编号',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_employee_no`(`employee_no` ASC, `tenant_id` ASC) USING BTREE,
  INDEX `idx_department_id`(`department_id` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '员工信息表';

-- 考勤记录表
CREATE TABLE `jishu_hr_attendance` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '考勤ID',
  `employee_id` bigint NOT NULL COMMENT '员工ID',
  `attendance_date` date NOT NULL COMMENT '考勤日期',
  `check_in_time` datetime NULL DEFAULT NULL COMMENT '签到时间',
  `check_out_time` datetime NULL DEFAULT NULL COMMENT '签退时间',
  `work_hours` decimal(4,2) NULL DEFAULT NULL COMMENT '工作时长',
  `status` tinyint NOT NULL DEFAULT 0 COMMENT '考勤状态（0正常 1迟到 2早退 3缺勤）',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '备注',
  `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT 0 COMMENT '租户编号',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_employee_date`(`employee_id` ASC, `attendance_date` ASC, `tenant_id` ASC) USING BTREE,
  INDEX `idx_attendance_date`(`attendance_date` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '考勤记录表';
```

##### 财务模块表
```sql
-- 费用申请表
CREATE TABLE `jishu_finance_expense` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '费用ID',
  `expense_no` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '费用单号',
  `employee_id` bigint NOT NULL COMMENT '申请人ID',
  `expense_type` tinyint NOT NULL COMMENT '费用类型（1差旅费 2油费 3维修费 4其他）',
  `amount` decimal(10,2) NOT NULL COMMENT '费用金额',
  `expense_date` date NOT NULL COMMENT '费用发生日期',
  `description` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '费用说明',
  `status` tinyint NOT NULL DEFAULT 0 COMMENT '审核状态（0待审核 1已通过 2已拒绝）',
  `audit_user_id` bigint NULL DEFAULT NULL COMMENT '审核人ID',
  `audit_time` datetime NULL DEFAULT NULL COMMENT '审核时间',
  `audit_remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '审核备注',
  `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT 0 COMMENT '租户编号',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_expense_no`(`expense_no` ASC) USING BTREE,
  INDEX `idx_employee_id`(`employee_id` ASC) USING BTREE,
  INDEX `idx_expense_date`(`expense_date` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '费用申请表';
```

##### 调度模块表
```sql
-- 任务表
CREATE TABLE `jishu_dispatch_task` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '任务ID',
  `task_no` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '任务编号',
  `task_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '任务名称',
  `task_type` tinyint NOT NULL COMMENT '任务类型（1运输 2配送 3巡检 4其他）',
  `start_location` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '起始地点',
  `end_location` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '目的地点',
  `planned_start_time` datetime NOT NULL COMMENT '计划开始时间',
  `planned_end_time` datetime NOT NULL COMMENT '计划结束时间',
  `driver_id` bigint NULL DEFAULT NULL COMMENT '分配司机ID',
  `vehicle_id` bigint NULL DEFAULT NULL COMMENT '分配车辆ID',
  `status` tinyint NOT NULL DEFAULT 0 COMMENT '任务状态（0待分配 1已分配 2进行中 3已完成 4已取消）',
  `priority` tinyint NOT NULL DEFAULT 1 COMMENT '优先级（1低 2中 3高）',
  `description` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '任务描述',
  `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT 0 COMMENT '租户编号',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_task_no`(`task_no` ASC) USING BTREE,
  INDEX `idx_driver_id`(`driver_id` ASC) USING BTREE,
  INDEX `idx_vehicle_id`(`vehicle_id` ASC) USING BTREE,
  INDEX `idx_planned_start_time`(`planned_start_time` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '任务表';

-- 车辆信息表
CREATE TABLE `jishu_dispatch_vehicle` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '车辆ID',
  `vehicle_no` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '车牌号',
  `vehicle_type` tinyint NOT NULL COMMENT '车辆类型（1小型车 2中型车 3大型车）',
  `brand` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '车辆品牌',
  `model` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '车辆型号',
  `load_capacity` decimal(8,2) NULL DEFAULT NULL COMMENT '载重量（吨）',
  `fuel_type` tinyint NOT NULL DEFAULT 1 COMMENT '燃料类型（1汽油 2柴油 3电动 4混合动力）',
  `status` tinyint NOT NULL DEFAULT 0 COMMENT '车辆状态（0空闲 1使用中 2维修中 3停用）',
  `current_driver_id` bigint NULL DEFAULT NULL COMMENT '当前司机ID',
  `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT 0 COMMENT '租户编号',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_vehicle_no`(`vehicle_no` ASC, `tenant_id` ASC) USING BTREE,
  INDEX `idx_current_driver_id`(`current_driver_id` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '车辆信息表';
```

##### 司机模块表
```sql
-- 司机任务执行记录表
CREATE TABLE `jishu_driver_task_record` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `task_id` bigint NOT NULL COMMENT '任务ID',
  `driver_id` bigint NOT NULL COMMENT '司机ID',
  `action_type` tinyint NOT NULL COMMENT '操作类型（1接受任务 2开始执行 3到达起点 4到达终点 5完成任务）',
  `action_time` datetime NOT NULL COMMENT '操作时间',
  `location_lat` decimal(10,6) NULL DEFAULT NULL COMMENT '纬度',
  `location_lng` decimal(10,6) NULL DEFAULT NULL COMMENT '经度',
  `location_address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '位置地址',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '备注',
  `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT 0 COMMENT '租户编号',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_task_id`(`task_id` ASC) USING BTREE,
  INDEX `idx_driver_id`(`driver_id` ASC) USING BTREE,
  INDEX `idx_action_time`(`action_time` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '司机任务执行记录表';
```

## 代码开发规范

### 1. 包结构规范

#### 后端包结构
```
com.nodal.module.jishu.{模块}.{层级}
```

示例：
```
com.nodal.module.jishu.user.controller     # 用户模块控制器
com.nodal.module.jishu.user.service        # 用户模块服务层
com.nodal.module.jishu.user.dal.dataobject # 用户模块数据对象
com.nodal.module.jishu.order.controller    # 订单模块控制器
com.nodal.module.jishu.order.service       # 订单模块服务层
```

#### 前端目录结构
```
src/
├── api/jishu/              # jishu业务API
│   ├── user/              # 用户相关API
│   ├── order/             # 订单相关API
│   └── product/           # 产品相关API
├── views/jishu/           # jishu业务页面
│   ├── user/              # 用户管理页面
│   ├── order/             # 订单管理页面
│   └── product/           # 产品管理页面
```

### 2. 类命名规范

#### 后端类命名
```java
// 数据对象
JishuUserProfileDO
JishuOrderMainDO

// 控制器
JishuUserController
JishuOrderController

// 服务类
JishuUserService
JishuOrderService

// 请求响应对象
JishuUserCreateReqVO
JishuUserPageReqVO
JishuUserRespVO
```

#### 前端文件命名
```typescript
// API文件
jishu-user.ts
jishu-order.ts

// 页面组件
JishuUserList.vue
JishuOrderDetail.vue

// 类型定义
jishu-user.d.ts
jishu-order.d.ts
```

### 3. API接口规范

#### RESTful API设计
```
GET    /admin-api/jishu/user/page          # 用户分页查询
GET    /admin-api/jishu/user/{id}          # 用户详情查询
POST   /admin-api/jishu/user               # 创建用户
PUT    /admin-api/jishu/user/{id}          # 更新用户
DELETE /admin-api/jishu/user/{id}          # 删除用户

GET    /admin-api/jishu/order/page         # 订单分页查询
GET    /admin-api/jishu/order/{id}         # 订单详情查询
POST   /admin-api/jishu/order              # 创建订单
PUT    /admin-api/jishu/order/{id}         # 更新订单
```

## 配置管理规范

### 1. 配置文件组织
```yaml
# application-jishu.yaml
jishu:
  user:
    default-avatar: "https://example.com/default-avatar.png"
    max-points: 999999
  order:
    auto-cancel-minutes: 30
    max-items-per-order: 100
  sms:
    template-id: "SMS_123456789"
    sign-name: "技术平台"
```

### 2. 字典数据规范
```sql
-- 基于角色的字典类型定义
INSERT INTO system_dict_type (name, type, status, remark) VALUES
-- 人事模块字典
('员工状态', 'jishu_hr_employee_status', 0, '员工状态字典'),
('考勤状态', 'jishu_hr_attendance_status', 0, '考勤状态字典'),
('部门类型', 'jishu_hr_department_type', 0, '部门类型字典'),

-- 财务模块字典
('费用类型', 'jishu_finance_expense_type', 0, '费用类型字典'),
('审核状态', 'jishu_finance_audit_status', 0, '审核状态字典'),
('支付方式', 'jishu_finance_payment_method', 0, '支付方式字典'),

-- 调度模块字典
('任务类型', 'jishu_dispatch_task_type', 0, '任务类型字典'),
('任务状态', 'jishu_dispatch_task_status', 0, '任务状态字典'),
('车辆类型', 'jishu_dispatch_vehicle_type', 0, '车辆类型字典'),
('车辆状态', 'jishu_dispatch_vehicle_status', 0, '车辆状态字典'),
('燃料类型', 'jishu_dispatch_fuel_type', 0, '燃料类型字典'),

-- 司机模块字典
('操作类型', 'jishu_driver_action_type', 0, '司机操作类型字典'),
('驾驶证类型', 'jishu_driver_license_type', 0, '驾驶证类型字典');

-- 字典数据详细配置
-- 员工状态
INSERT INTO system_dict_data (dict_type, label, value, sort, status, remark) VALUES
('jishu_hr_employee_status', '正常', '0', 1, 0, '员工正常在职'),
('jishu_hr_employee_status', '离职', '1', 2, 0, '员工已离职'),
('jishu_hr_employee_status', '停职', '2', 3, 0, '员工暂时停职');

-- 考勤状态
INSERT INTO system_dict_data (dict_type, label, value, sort, status, remark) VALUES
('jishu_hr_attendance_status', '正常', '0', 1, 0, '正常出勤'),
('jishu_hr_attendance_status', '迟到', '1', 2, 0, '迟到'),
('jishu_hr_attendance_status', '早退', '2', 3, 0, '早退'),
('jishu_hr_attendance_status', '缺勤', '3', 4, 0, '缺勤');

-- 费用类型
INSERT INTO system_dict_data (dict_type, label, value, sort, status, remark) VALUES
('jishu_finance_expense_type', '差旅费', '1', 1, 0, '出差相关费用'),
('jishu_finance_expense_type', '油费', '2', 2, 0, '车辆燃油费用'),
('jishu_finance_expense_type', '维修费', '3', 3, 0, '车辆维修费用'),
('jishu_finance_expense_type', '其他', '4', 4, 0, '其他费用');

-- 审核状态
INSERT INTO system_dict_data (dict_type, label, value, sort, status, remark) VALUES
('jishu_finance_audit_status', '待审核', '0', 1, 0, '等待审核'),
('jishu_finance_audit_status', '已通过', '1', 2, 0, '审核通过'),
('jishu_finance_audit_status', '已拒绝', '2', 3, 0, '审核拒绝');

-- 任务类型
INSERT INTO system_dict_data (dict_type, label, value, sort, status, remark) VALUES
('jishu_dispatch_task_type', '运输', '1', 1, 0, '货物运输任务'),
('jishu_dispatch_task_type', '配送', '2', 2, 0, '配送任务'),
('jishu_dispatch_task_type', '巡检', '3', 3, 0, '巡检任务'),
('jishu_dispatch_task_type', '其他', '4', 4, 0, '其他任务');

-- 任务状态
INSERT INTO system_dict_data (dict_type, label, value, sort, status, remark) VALUES
('jishu_dispatch_task_status', '待分配', '0', 1, 0, '等待分配司机'),
('jishu_dispatch_task_status', '已分配', '1', 2, 0, '已分配司机'),
('jishu_dispatch_task_status', '进行中', '2', 3, 0, '任务执行中'),
('jishu_dispatch_task_status', '已完成', '3', 4, 0, '任务已完成'),
('jishu_dispatch_task_status', '已取消', '4', 5, 0, '任务已取消');

-- 车辆类型
INSERT INTO system_dict_data (dict_type, label, value, sort, status, remark) VALUES
('jishu_dispatch_vehicle_type', '小型车', '1', 1, 0, '小型载货车'),
('jishu_dispatch_vehicle_type', '中型车', '2', 2, 0, '中型载货车'),
('jishu_dispatch_vehicle_type', '大型车', '3', 3, 0, '大型载货车');

-- 车辆状态
INSERT INTO system_dict_data (dict_type, label, value, sort, status, remark) VALUES
('jishu_dispatch_vehicle_status', '空闲', '0', 1, 0, '车辆空闲可用'),
('jishu_dispatch_vehicle_status', '使用中', '1', 2, 0, '车辆使用中'),
('jishu_dispatch_vehicle_status', '维修中', '2', 3, 0, '车辆维修中'),
('jishu_dispatch_vehicle_status', '停用', '3', 4, 0, '车辆停用');

-- 燃料类型
INSERT INTO system_dict_data (dict_type, label, value, sort, status, remark) VALUES
('jishu_dispatch_fuel_type', '汽油', '1', 1, 0, '汽油车'),
('jishu_dispatch_fuel_type', '柴油', '2', 2, 0, '柴油车'),
('jishu_dispatch_fuel_type', '电动', '3', 3, 0, '电动车'),
('jishu_dispatch_fuel_type', '混合动力', '4', 4, 0, '混合动力车');

-- 司机操作类型
INSERT INTO system_dict_data (dict_type, label, value, sort, status, remark) VALUES
('jishu_driver_action_type', '接受任务', '1', 1, 0, '司机接受任务'),
('jishu_driver_action_type', '开始执行', '2', 2, 0, '开始执行任务'),
('jishu_driver_action_type', '到达起点', '3', 3, 0, '到达起始地点'),
('jishu_driver_action_type', '到达终点', '4', 4, 0, '到达目的地点'),
('jishu_driver_action_type', '完成任务', '5', 5, 0, '完成任务');
```

## 权限管理规范

### 1. 系统角色定义
系统基于业务场景定义四个核心角色：

#### 角色类型
```sql
-- 系统角色数据
INSERT INTO system_role (name, code, sort, status, type, remark) VALUES
('人事管理员', 'jishu_hr', 1, 0, 2, '负责员工管理、考勤管理等人事相关业务'),
('财务管理员', 'jishu_finance', 2, 0, 2, '负责财务管理、费用审核、报销等财务相关业务'),
('调度管理员', 'jishu_dispatch', 3, 0, 2, '负责任务调度、车辆调度、路线规划等调度相关业务'),
('司机', 'jishu_driver', 4, 0, 2, '负责车辆驾驶、任务执行、状态上报等司机相关业务');
```

### 2. 权限标识规范
```
jishu:{模块}:{操作}:{角色}
```

#### 人事模块权限
```
jishu:hr:employee:query      # 员工查询
jishu:hr:employee:create     # 员工创建
jishu:hr:employee:update     # 员工更新
jishu:hr:employee:delete     # 员工删除
jishu:hr:attendance:query    # 考勤查询
jishu:hr:attendance:manage   # 考勤管理
jishu:hr:salary:query        # 薪资查询
jishu:hr:salary:manage       # 薪资管理
```

#### 财务模块权限
```
jishu:finance:expense:query     # 费用查询
jishu:finance:expense:audit     # 费用审核
jishu:finance:reimburse:query   # 报销查询
jishu:finance:reimburse:audit   # 报销审核
jishu:finance:salary:query      # 薪资查询
jishu:finance:salary:pay        # 薪资发放
jishu:finance:report:query      # 财务报表查询
```

#### 调度模块权限
```
jishu:dispatch:task:query       # 任务查询
jishu:dispatch:task:create      # 任务创建
jishu:dispatch:task:assign      # 任务分配
jishu:dispatch:vehicle:query    # 车辆查询
jishu:dispatch:vehicle:manage   # 车辆管理
jishu:dispatch:route:query      # 路线查询
jishu:dispatch:route:plan       # 路线规划
```

#### 司机模块权限
```
jishu:driver:task:query         # 任务查询
jishu:driver:task:accept        # 任务接受
jishu:driver:task:report        # 任务上报
jishu:driver:vehicle:query      # 车辆信息查询
jishu:driver:location:update    # 位置更新
jishu:driver:expense:submit     # 费用提交
```

### 3. 菜单配置规范
```sql
-- 主菜单
INSERT INTO system_menu (name, permission, type, sort, parent_id, path, icon, component, status) VALUES
('技术管理', '', 1, 100, 0, '/jishu', 'system', '', 0);

-- 人事管理菜单
INSERT INTO system_menu (name, permission, type, sort, parent_id, path, icon, component, status) VALUES
('人事管理', '', 1, 1, (SELECT id FROM system_menu WHERE path = '/jishu'), '/jishu/hr', 'peoples', '', 0),
('员工管理', 'jishu:hr:employee:query', 2, 1, (SELECT id FROM system_menu WHERE path = '/jishu/hr'), 'employee', 'user', 'jishu/hr/employee/index', 0),
('考勤管理', 'jishu:hr:attendance:query', 2, 2, (SELECT id FROM system_menu WHERE path = '/jishu/hr'), 'attendance', 'time', 'jishu/hr/attendance/index', 0),
('薪资管理', 'jishu:hr:salary:query', 2, 3, (SELECT id FROM system_menu WHERE path = '/jishu/hr'), 'salary', 'money', 'jishu/hr/salary/index', 0);

-- 财务管理菜单
INSERT INTO system_menu (name, permission, type, sort, parent_id, path, icon, component, status) VALUES
('财务管理', '', 1, 2, (SELECT id FROM system_menu WHERE path = '/jishu'), '/jishu/finance', 'money', '', 0),
('费用管理', 'jishu:finance:expense:query', 2, 1, (SELECT id FROM system_menu WHERE path = '/jishu/finance'), 'expense', 'bill', 'jishu/finance/expense/index', 0),
('报销管理', 'jishu:finance:reimburse:query', 2, 2, (SELECT id FROM system_menu WHERE path = '/jishu/finance'), 'reimburse', 'receipt', 'jishu/finance/reimburse/index', 0),
('财务报表', 'jishu:finance:report:query', 2, 3, (SELECT id FROM system_menu WHERE path = '/jishu/finance'), 'report', 'chart', 'jishu/finance/report/index', 0);

-- 调度管理菜单
INSERT INTO system_menu (name, permission, type, sort, parent_id, path, icon, component, status) VALUES
('调度管理', '', 1, 3, (SELECT id FROM system_menu WHERE path = '/jishu'), '/jishu/dispatch', 'coordinate', '', 0),
('任务管理', 'jishu:dispatch:task:query', 2, 1, (SELECT id FROM system_menu WHERE path = '/jishu/dispatch'), 'task', 'list', 'jishu/dispatch/task/index', 0),
('车辆管理', 'jishu:dispatch:vehicle:query', 2, 2, (SELECT id FROM system_menu WHERE path = '/jishu/dispatch'), 'vehicle', 'car', 'jishu/dispatch/vehicle/index', 0),
('路线管理', 'jishu:dispatch:route:query', 2, 3, (SELECT id FROM system_menu WHERE path = '/jishu/dispatch'), 'route', 'route', 'jishu/dispatch/route/index', 0);

-- 司机端菜单
INSERT INTO system_menu (name, permission, type, sort, parent_id, path, icon, component, status) VALUES
('司机工作台', '', 1, 4, (SELECT id FROM system_menu WHERE path = '/jishu'), '/jishu/driver', 'user', '', 0),
('我的任务', 'jishu:driver:task:query', 2, 1, (SELECT id FROM system_menu WHERE path = '/jishu/driver'), 'my-task', 'list', 'jishu/driver/task/index', 0),
('车辆信息', 'jishu:driver:vehicle:query', 2, 2, (SELECT id FROM system_menu WHERE path = '/jishu/driver'), 'my-vehicle', 'car', 'jishu/driver/vehicle/index', 0),
('费用提交', 'jishu:driver:expense:submit', 2, 3, (SELECT id FROM system_menu WHERE path = '/jishu/driver'), 'expense-submit', 'bill', 'jishu/driver/expense/index', 0);
```

### 4. 角色权限分配
```sql
-- 人事管理员权限分配
INSERT INTO system_role_menu (role_id, menu_id)
SELECT r.id, m.id FROM system_role r, system_menu m
WHERE r.code = 'jishu_hr' AND m.permission LIKE 'jishu:hr:%';

-- 财务管理员权限分配
INSERT INTO system_role_menu (role_id, menu_id)
SELECT r.id, m.id FROM system_role r, system_menu m
WHERE r.code = 'jishu_finance' AND (m.permission LIKE 'jishu:finance:%' OR m.permission LIKE 'jishu:hr:salary:%');

-- 调度管理员权限分配
INSERT INTO system_role_menu (role_id, menu_id)
SELECT r.id, m.id FROM system_role r, system_menu m
WHERE r.code = 'jishu_dispatch' AND m.permission LIKE 'jishu:dispatch:%';

-- 司机权限分配
INSERT INTO system_role_menu (role_id, menu_id)
SELECT r.id, m.id FROM system_role r, system_menu m
WHERE r.code = 'jishu_driver' AND m.permission LIKE 'jishu:driver:%';
```

## 测试规范

### 1. 单元测试命名
```java
// 测试类命名
JishuUserServiceTest
JishuOrderControllerTest

// 测试方法命名
testCreateUser_Success()
testCreateUser_WithInvalidParam_ThrowException()
```

### 2. 测试数据准备
```java
// 测试数据以 jishu 开头
@Sql("/sql/jishu-user-test-data.sql")
@Sql("/sql/jishu-order-test-data.sql")
```

## 文档规范

### 1. API文档注解
```java
@Tag(name = "管理后台 - 技术用户")
@RestController
@RequestMapping("/admin-api/jishu/user")
public class JishuUserController {
    
    @Operation(summary = "创建技术用户")
    @PostMapping
    public CommonResult<Long> createUser(@Valid @RequestBody JishuUserCreateReqVO reqVO) {
        // 实现逻辑
    }
}
```

### 2. 数据库文档
- 表注释必须包含 "技术平台" 前缀
- 字段注释要清晰明确
- 重要业务表需要提供设计说明文档

## 版本管理规范

### 1. Git分支规范
```
feature/jishu-user-management    # 用户管理功能
feature/jishu-order-system       # 订单系统功能
bugfix/jishu-user-login-issue    # 用户登录问题修复
```

### 2. 提交信息规范
```
feat(jishu-user): 添加用户积分管理功能
fix(jishu-order): 修复订单状态更新问题
docs(jishu): 更新开发规范文档
```

## 部署规范

### 1. 环境变量
```bash
# 技术平台相关配置
JISHU_USER_DEFAULT_AVATAR=https://example.com/avatar.png
JISHU_ORDER_AUTO_CANCEL_MINUTES=30
JISHU_SMS_TEMPLATE_ID=SMS_123456789
```

### 2. 配置文件
```yaml
# 生产环境配置
jishu:
  enabled: true
  modules:
    user: true
    order: true
    product: true
```

---

*本规范适用于所有基于 jishu_ 前缀的新业务开发，请严格遵守以确保代码质量和项目一致性。*
