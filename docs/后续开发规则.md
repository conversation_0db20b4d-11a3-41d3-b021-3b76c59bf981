# 后续开发规则文档

## 数据库表设计规范

### 1. 表命名统一规范

#### 新业务表统一前缀
所有新开发的业务表必须以 `jishu_` 作为前缀，格式为：
```
jishu_{模块}_{业务对象}
```

#### 模块划分规范
```
jishu_user_*        # 用户相关业务
jishu_order_*       # 订单管理
jishu_product_*     # 产品管理
jishu_finance_*     # 财务管理
jishu_workflow_*    # 工作流业务
jishu_report_*      # 报表统计
jishu_message_*     # 消息通知
jishu_file_*        # 文件管理
jishu_log_*         # 业务日志
jishu_config_*      # 业务配置
jishu_wechat_*      # 微信集成
jishu_alipay_*      # 支付宝集成
jishu_sms_*         # 短信服务
jishu_email_*       # 邮件服务
```

### 2. 表设计示例

#### 用户业务表示例
```sql
-- 用户扩展信息表
CREATE TABLE `jishu_user_profile` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `real_name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '真实姓名',
  `id_card` varchar(18) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '身份证号',
  `address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '详细地址',
  `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT 0 COMMENT '租户编号',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_user_id`(`user_id` ASC) USING BTREE,
  INDEX `idx_tenant_id`(`tenant_id` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '用户扩展信息表';

-- 用户积分表
CREATE TABLE `jishu_user_points` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '主键ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `total_points` int NOT NULL DEFAULT 0 COMMENT '总积分',
  `available_points` int NOT NULL DEFAULT 0 COMMENT '可用积分',
  `frozen_points` int NOT NULL DEFAULT 0 COMMENT '冻结积分',
  `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT 0 COMMENT '租户编号',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_user_tenant`(`user_id` ASC, `tenant_id` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '用户积分表';
```

#### 订单业务表示例
```sql
-- 订单主表
CREATE TABLE `jishu_order_main` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '订单ID',
  `order_no` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '订单号',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `order_status` tinyint NOT NULL DEFAULT 0 COMMENT '订单状态',
  `total_amount` decimal(10,2) NOT NULL DEFAULT 0.00 COMMENT '订单总金额',
  `pay_amount` decimal(10,2) NOT NULL DEFAULT 0.00 COMMENT '实付金额',
  `pay_status` tinyint NOT NULL DEFAULT 0 COMMENT '支付状态',
  `pay_time` datetime NULL DEFAULT NULL COMMENT '支付时间',
  `remark` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '订单备注',
  `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT 0 COMMENT '租户编号',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_order_no`(`order_no` ASC) USING BTREE,
  INDEX `idx_user_id`(`user_id` ASC) USING BTREE,
  INDEX `idx_create_time`(`create_time` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '订单主表';
```

## 代码开发规范

### 1. 包结构规范

#### 后端包结构
```
com.nodal.module.jishu.{模块}.{层级}
```

示例：
```
com.nodal.module.jishu.user.controller     # 用户模块控制器
com.nodal.module.jishu.user.service        # 用户模块服务层
com.nodal.module.jishu.user.dal.dataobject # 用户模块数据对象
com.nodal.module.jishu.order.controller    # 订单模块控制器
com.nodal.module.jishu.order.service       # 订单模块服务层
```

#### 前端目录结构
```
src/
├── api/jishu/              # jishu业务API
│   ├── user/              # 用户相关API
│   ├── order/             # 订单相关API
│   └── product/           # 产品相关API
├── views/jishu/           # jishu业务页面
│   ├── user/              # 用户管理页面
│   ├── order/             # 订单管理页面
│   └── product/           # 产品管理页面
```

### 2. 类命名规范

#### 后端类命名
```java
// 数据对象
JishuUserProfileDO
JishuOrderMainDO

// 控制器
JishuUserController
JishuOrderController

// 服务类
JishuUserService
JishuOrderService

// 请求响应对象
JishuUserCreateReqVO
JishuUserPageReqVO
JishuUserRespVO
```

#### 前端文件命名
```typescript
// API文件
jishu-user.ts
jishu-order.ts

// 页面组件
JishuUserList.vue
JishuOrderDetail.vue

// 类型定义
jishu-user.d.ts
jishu-order.d.ts
```

### 3. API接口规范

#### RESTful API设计
```
GET    /admin-api/jishu/user/page          # 用户分页查询
GET    /admin-api/jishu/user/{id}          # 用户详情查询
POST   /admin-api/jishu/user               # 创建用户
PUT    /admin-api/jishu/user/{id}          # 更新用户
DELETE /admin-api/jishu/user/{id}          # 删除用户

GET    /admin-api/jishu/order/page         # 订单分页查询
GET    /admin-api/jishu/order/{id}         # 订单详情查询
POST   /admin-api/jishu/order              # 创建订单
PUT    /admin-api/jishu/order/{id}         # 更新订单
```

## 配置管理规范

### 1. 配置文件组织
```yaml
# application-jishu.yaml
jishu:
  user:
    default-avatar: "https://example.com/default-avatar.png"
    max-points: 999999
  order:
    auto-cancel-minutes: 30
    max-items-per-order: 100
  sms:
    template-id: "SMS_123456789"
    sign-name: "技术平台"
```

### 2. 字典数据规范
```sql
-- 字典类型以 jishu_ 开头
INSERT INTO system_dict_type (name, type, status, remark) VALUES 
('技术用户状态', 'jishu_user_status', 0, '技术平台用户状态'),
('技术订单状态', 'jishu_order_status', 0, '技术平台订单状态');

-- 字典数据
INSERT INTO system_dict_data (dict_type, label, value, sort, status) VALUES 
('jishu_user_status', '正常', '0', 1, 0),
('jishu_user_status', '禁用', '1', 2, 0),
('jishu_order_status', '待支付', '0', 1, 0),
('jishu_order_status', '已支付', '1', 2, 0);
```

## 权限管理规范

### 1. 权限标识规范
```
jishu:{模块}:{操作}
```

示例：
```
jishu:user:query     # 用户查询权限
jishu:user:create    # 用户创建权限
jishu:user:update    # 用户更新权限
jishu:user:delete    # 用户删除权限
jishu:order:query    # 订单查询权限
jishu:order:create   # 订单创建权限
```

### 2. 菜单配置规范
```sql
-- 菜单路径以 /jishu 开头
INSERT INTO system_menu (name, permission, type, sort, parent_id, path, icon, component, status) VALUES 
('技术管理', '', 1, 100, 0, '/jishu', 'system', '', 0),
('用户管理', 'jishu:user:query', 2, 1, (SELECT id FROM system_menu WHERE path = '/jishu'), 'user', 'user', 'jishu/user/index', 0);
```

## 测试规范

### 1. 单元测试命名
```java
// 测试类命名
JishuUserServiceTest
JishuOrderControllerTest

// 测试方法命名
testCreateUser_Success()
testCreateUser_WithInvalidParam_ThrowException()
```

### 2. 测试数据准备
```java
// 测试数据以 jishu 开头
@Sql("/sql/jishu-user-test-data.sql")
@Sql("/sql/jishu-order-test-data.sql")
```

## 文档规范

### 1. API文档注解
```java
@Tag(name = "管理后台 - 技术用户")
@RestController
@RequestMapping("/admin-api/jishu/user")
public class JishuUserController {
    
    @Operation(summary = "创建技术用户")
    @PostMapping
    public CommonResult<Long> createUser(@Valid @RequestBody JishuUserCreateReqVO reqVO) {
        // 实现逻辑
    }
}
```

### 2. 数据库文档
- 表注释必须包含 "技术平台" 前缀
- 字段注释要清晰明确
- 重要业务表需要提供设计说明文档

## 版本管理规范

### 1. Git分支规范
```
feature/jishu-user-management    # 用户管理功能
feature/jishu-order-system       # 订单系统功能
bugfix/jishu-user-login-issue    # 用户登录问题修复
```

### 2. 提交信息规范
```
feat(jishu-user): 添加用户积分管理功能
fix(jishu-order): 修复订单状态更新问题
docs(jishu): 更新开发规范文档
```

## 部署规范

### 1. 环境变量
```bash
# 技术平台相关配置
JISHU_USER_DEFAULT_AVATAR=https://example.com/avatar.png
JISHU_ORDER_AUTO_CANCEL_MINUTES=30
JISHU_SMS_TEMPLATE_ID=SMS_123456789
```

### 2. 配置文件
```yaml
# 生产环境配置
jishu:
  enabled: true
  modules:
    user: true
    order: true
    product: true
```

---

*本规范适用于所有基于 jishu_ 前缀的新业务开发，请严格遵守以确保代码质量和项目一致性。*
