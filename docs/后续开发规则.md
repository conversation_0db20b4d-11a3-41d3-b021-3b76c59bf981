# 后续开发规则文档

## 数据库表设计规范

### 1. 表命名统一规范

#### 新业务表统一前缀
所有新开发的业务表必须以 `jishu_` 作为前缀，格式为：
```
jishu_{模块}_{业务对象}
```

#### 模块划分规范
基于系统角色划分的核心业务模块：
```
jishu_hr_*          # 人事管理模块
jishu_finance_*     # 财务管理模块
jishu_dispatch_*    # 调度管理模块
jishu_driver_*      # 司机业务模块
```

#### 扩展业务模块
```
jishu_user_*        # 用户相关业务
jishu_message_*     # 消息通知模块
jishu_file_*        # 文件管理模块
jishu_log_*         # 业务日志模块
jishu_config_*      # 业务配置模块
jishu_report_*      # 报表统计模块
```

## 系统角色规范

### 1. 系统角色定义
系统基于业务场景定义四个核心角色：

#### 角色类型
```sql
-- 系统角色数据
INSERT INTO system_role (name, code, sort, status, type, remark) VALUES
('人事管理员', 'jishu_hr', 1, 0, 2, '负责员工管理、考勤管理等人事相关业务'),
('财务管理员', 'jishu_finance', 2, 0, 2, '负责财务管理、费用审核、报销等财务相关业务'),
('调度管理员', 'jishu_dispatch', 3, 0, 2, '负责任务调度、车辆调度、路线规划等调度相关业务'),
('司机', 'jishu_driver', 4, 0, 2, '负责车辆驾驶、任务执行、状态上报等司机相关业务');
```

### 2. 角色职责说明

| 角色代码 | 角色名称 | 主要职责 | 权限范围 |
|---------|---------|---------|---------|
| `jishu_hr` | 人事管理员 | 员工管理、考勤管理、薪资管理 | 人事模块全部权限 |
| `jishu_finance` | 财务管理员 | 费用审核、报销管理、财务报表 | 财务模块全部权限 + 薪资查询权限 |
| `jishu_dispatch` | 调度管理员 | 任务调度、车辆管理、路线规划 | 调度模块全部权限 |
| `jishu_driver` | 司机 | 任务执行、状态上报、费用提交 | 司机模块权限 + 只读权限 |

### 3. 表设计示例

#### 人事模块表示例
```sql
-- 员工信息表
CREATE TABLE `jishu_hr_employee` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '员工ID',
  `employee_no` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '员工编号',
  `name` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '员工姓名',
  `phone` varchar(11) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '手机号码',
  `id_card` varchar(18) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '身份证号',
  `department_id` bigint NULL DEFAULT NULL COMMENT '部门ID',
  `position` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '职位',
  `entry_date` date NOT NULL COMMENT '入职日期',
  `status` tinyint NOT NULL DEFAULT 0 COMMENT '员工状态（0正常 1离职）',
  `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT 0 COMMENT '租户编号',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_employee_no`(`employee_no` ASC, `tenant_id` ASC) USING BTREE,
  INDEX `idx_department_id`(`department_id` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '员工信息表';
```

#### 财务模块表示例
```sql
-- 费用申请表
CREATE TABLE `jishu_finance_expense` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '费用ID',
  `expense_no` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '费用单号',
  `employee_id` bigint NOT NULL COMMENT '申请人ID',
  `expense_type` tinyint NOT NULL COMMENT '费用类型（1差旅费 2油费 3维修费 4其他）',
  `amount` decimal(10,2) NOT NULL COMMENT '费用金额',
  `expense_date` date NOT NULL COMMENT '费用发生日期',
  `description` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '费用说明',
  `status` tinyint NOT NULL DEFAULT 0 COMMENT '审核状态（0待审核 1已通过 2已拒绝）',
  `audit_user_id` bigint NULL DEFAULT NULL COMMENT '审核人ID',
  `audit_time` datetime NULL DEFAULT NULL COMMENT '审核时间',
  `audit_remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '审核备注',
  `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT 0 COMMENT '租户编号',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_expense_no`(`expense_no` ASC) USING BTREE,
  INDEX `idx_employee_id`(`employee_id` ASC) USING BTREE,
  INDEX `idx_expense_date`(`expense_date` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '费用申请表';
```

#### 调度模块表示例
```sql
-- 任务表
CREATE TABLE `jishu_dispatch_task` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '任务ID',
  `task_no` varchar(32) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '任务编号',
  `task_name` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '任务名称',
  `task_type` tinyint NOT NULL COMMENT '任务类型（1运输 2配送 3巡检 4其他）',
  `start_location` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '起始地点',
  `end_location` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL COMMENT '目的地点',
  `planned_start_time` datetime NOT NULL COMMENT '计划开始时间',
  `planned_end_time` datetime NOT NULL COMMENT '计划结束时间',
  `driver_id` bigint NULL DEFAULT NULL COMMENT '分配司机ID',
  `vehicle_id` bigint NULL DEFAULT NULL COMMENT '分配车辆ID',
  `status` tinyint NOT NULL DEFAULT 0 COMMENT '任务状态（0待分配 1已分配 2进行中 3已完成 4已取消）',
  `priority` tinyint NOT NULL DEFAULT 1 COMMENT '优先级（1低 2中 3高）',
  `description` varchar(500) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '任务描述',
  `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT 0 COMMENT '租户编号',
  PRIMARY KEY (`id`) USING BTREE,
  UNIQUE INDEX `uk_task_no`(`task_no` ASC) USING BTREE,
  INDEX `idx_driver_id`(`driver_id` ASC) USING BTREE,
  INDEX `idx_vehicle_id`(`vehicle_id` ASC) USING BTREE,
  INDEX `idx_planned_start_time`(`planned_start_time` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '任务表';
```

#### 司机模块表示例
```sql
-- 司机任务执行记录表
CREATE TABLE `jishu_driver_task_record` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `task_id` bigint NOT NULL COMMENT '任务ID',
  `driver_id` bigint NOT NULL COMMENT '司机ID',
  `action_type` tinyint NOT NULL COMMENT '操作类型（1接受任务 2开始执行 3到达起点 4到达终点 5完成任务）',
  `action_time` datetime NOT NULL COMMENT '操作时间',
  `location_lat` decimal(10,6) NULL DEFAULT NULL COMMENT '纬度',
  `location_lng` decimal(10,6) NULL DEFAULT NULL COMMENT '经度',
  `location_address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '位置地址',
  `remark` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT NULL COMMENT '备注',
  `creator` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '创建者',
  `create_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updater` varchar(64) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NULL DEFAULT '' COMMENT '更新者',
  `update_time` datetime NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` bit(1) NOT NULL DEFAULT b'0' COMMENT '是否删除',
  `tenant_id` bigint NOT NULL DEFAULT 0 COMMENT '租户编号',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `idx_task_id`(`task_id` ASC) USING BTREE,
  INDEX `idx_driver_id`(`driver_id` ASC) USING BTREE,
  INDEX `idx_action_time`(`action_time` ASC) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_unicode_ci COMMENT = '司机任务执行记录表';
```

---

*本规范适用于所有基于 jishu_ 前缀的新业务开发，请严格遵守以确保代码质量和项目一致性。*


