# 技术选型分析文档

## 项目概述

本项目是一个基于前后端分离架构的双端项目，包含管理后台前端（jishu-admin）和后端API服务（jishu-api）。

## 前端技术选型

### 核心框架
- **Vue 3.4.21** - 采用最新的Vue 3 Composition API，提供更好的TypeScript支持和性能
- **TypeScript 5.3.3** - 提供强类型支持，提高代码质量和开发效率
- **Vite 5.1.4** - 现代化构建工具，提供快速的开发体验和优化的生产构建

### UI组件库
- **Element Plus 2.7.0** - 基于Vue 3的企业级UI组件库
- **@element-plus/icons-vue 2.1.0** - Element Plus官方图标库
- **UnoCSS 0.58.5** - 原子化CSS引擎，提供高性能的样式解决方案

### 状态管理
- **Pinia 2.1.7** - Vue 3官方推荐的状态管理库
- **pinia-plugin-persistedstate 3.2.1** - Pinia持久化插件

### 路由管理
- **Vue Router 4.3.0** - Vue 3官方路由管理器

### 工具库
- **Axios 1.6.8** - HTTP客户端库
- **Lodash-es 4.17.21** - 实用工具库
- **Day.js 1.11.10** - 轻量级日期处理库
- **ECharts 5.5.0** - 数据可视化图表库

### 开发工具
- **ESLint 8.57.0** - 代码质量检查工具
- **Prettier 3.2.5** - 代码格式化工具
- **Sass 1.69.5** - CSS预处理器

### 特色功能组件
- **BPMN.js 8.9.0** - 工作流程图设计器
- **@wangeditor/editor 5.1.23** - 富文本编辑器
- **Cropper.js 1.6.1** - 图片裁剪组件
- **QRCode 1.5.3** - 二维码生成
- **Video.js 7.21.5** - 视频播放器

## 后端技术选型

### 核心框架
- **Spring Boot 3.3.1** - 基于Java 17的现代化Spring Boot框架
- **Java 17** - 采用LTS版本，提供最新的语言特性和性能优化

### 数据访问层
- **MyBatis Plus 3.5.7** - 增强版MyBatis，提供代码生成和CRUD操作简化
- **MyBatis Plus Join 1.4.13** - 支持关联查询的MyBatis Plus扩展
- **Druid 1.2.23** - 阿里巴巴数据库连接池
- **Dynamic DataSource 4.3.1** - 动态数据源切换

### 缓存和消息队列
- **Redis** - 缓存和会话存储
- **Redisson 3.32.0** - Redis分布式锁和数据结构
- **RocketMQ Spring 2.3.0** - 消息队列集成

### 安全和认证
- **Spring Security** - 安全框架
- **JWT** - 无状态认证

### 工作流引擎
- **Flowable 7.0.1** - 开源工作流和业务流程管理引擎

### 文档和API
- **SpringDoc 2.3.0** - OpenAPI 3规范文档生成
- **Knife4j 4.5.0** - Swagger UI增强版

### 工具库
- **Hutool 5.8.29** - Java工具类库
- **MapStruct 1.5.5.Final** - 对象映射框架
- **Lombok 1.18.34** - 代码生成注解
- **EasyExcel 4.0.1** - Excel处理库

### 数据库支持
- **MySQL** - 主要数据库
- **达梦数据库 ********** - 国产数据库支持
- **人大金仓 8.6.0** - 国产数据库支持
- **OpenGauss 5.0.2** - 华为开源数据库支持

### 监控和运维
- **Spring Boot Admin 3.3.2** - 应用监控
- **SkyWalking 9.0.0** - 分布式链路追踪

## 技术选型优势分析

### 前端技术优势
1. **现代化技术栈** - Vue 3 + TypeScript + Vite提供最佳开发体验
2. **组件化开发** - Element Plus提供丰富的企业级组件
3. **性能优化** - UnoCSS原子化CSS和Vite构建优化
4. **类型安全** - TypeScript提供编译时类型检查
5. **状态管理** - Pinia提供简洁的状态管理方案

### 后端技术优势
1. **企业级框架** - Spring Boot 3.x提供稳定可靠的基础
2. **数据库兼容性** - 支持多种主流和国产数据库
3. **微服务就绪** - 模块化设计支持微服务架构演进
4. **工作流支持** - Flowable提供完整的BPM解决方案
5. **高性能** - Redis缓存和连接池优化

## 架构特点

1. **前后端分离** - 清晰的职责分离，便于团队协作
2. **模块化设计** - 后端采用多模块Maven项目结构
3. **多租户支持** - 内置多租户架构设计
4. **国际化支持** - 前端支持多语言切换
5. **权限控制** - 基于RBAC的细粒度权限控制

## 部署和运维

1. **容器化支持** - 支持Docker容器化部署
2. **环境配置** - 支持多环境配置管理
3. **监控体系** - 完整的应用监控和链路追踪
4. **日志管理** - 结构化日志和集中式日志管理
