# 代码结构分析文档

## 项目整体结构

```
jishu/
├── jishu-admin/          # 前端管理后台
├── jishu-api/           # 后端API服务
├── jishu-doc/           # 项目文档
└── docs/                # 分析文档
```

## 前端项目结构 (jishu-admin)

### 目录结构
```
jishu-admin/
├── build/               # 构建配置
├── public/              # 静态资源
├── src/                 # 源代码
│   ├── api/            # API接口定义
│   │   ├── ai/         # AI模块接口
│   │   ├── bpm/        # 工作流接口
│   │   ├── crm/        # 客户关系管理接口
│   │   ├── erp/        # 企业资源规划接口
│   │   ├── infra/      # 基础设施接口
│   │   ├── login/      # 登录接口
│   │   ├── mall/       # 商城接口
│   │   ├── member/     # 会员接口
│   │   ├── mp/         # 微信公众号接口
│   │   ├── pay/        # 支付接口
│   │   └── system/     # 系统管理接口
│   ├── assets/         # 静态资源
│   ├── components/     # 公共组件
│   ├── config/         # 配置文件
│   ├── directives/     # 自定义指令
│   ├── hooks/          # 组合式API钩子
│   ├── layout/         # 布局组件
│   ├── locales/        # 国际化文件
│   ├── plugins/        # 插件配置
│   ├── router/         # 路由配置
│   ├── store/          # 状态管理
│   ├── styles/         # 样式文件
│   ├── types/          # TypeScript类型定义
│   ├── utils/          # 工具函数
│   └── views/          # 页面组件
├── types/              # 全局类型定义
├── package.json        # 项目依赖
├── vite.config.ts      # Vite配置
├── tsconfig.json       # TypeScript配置
└── uno.config.ts       # UnoCSS配置
```

### 前端架构特点

1. **模块化API管理** - 按业务模块组织API接口
2. **组件化开发** - 丰富的公共组件库
3. **类型安全** - 完整的TypeScript类型定义
4. **国际化支持** - 多语言配置
5. **状态管理** - Pinia状态管理
6. **路由管理** - 动态路由配置

## 后端项目结构 (jishu-api)

### 总体架构
```
jishu-api/
├── nodal-dependencies/           # 依赖管理模块
├── nodal-framework/             # 框架核心模块
├── nodal-server/                # 服务启动模块
├── nodal-module-system/         # 系统管理模块
├── nodal-module-infra/          # 基础设施模块
├── nodal-module-member/         # 会员模块(可选)
├── nodal-module-bpm/            # 工作流模块(可选)
├── nodal-module-report/         # 报表模块(可选)
├── nodal-module-mp/             # 微信公众号模块(可选)
├── nodal-module-pay/            # 支付模块(可选)
├── nodal-module-mall/           # 商城模块(可选)
├── nodal-module-crm/            # 客户关系管理模块(可选)
├── nodal-module-erp/            # 企业资源规划模块(可选)
├── nodal-module-ai/             # AI模块(可选)
├── sql/                         # 数据库脚本
├── script/                      # 部署脚本
├── logs/                        # 日志目录
└── pom.xml                      # Maven主配置
```

### 框架核心模块 (nodal-framework)
```
nodal-framework/
├── nodal-common/                           # 公共模块
├── nodal-spring-boot-starter-web/          # Web启动器
├── nodal-spring-boot-starter-security/     # 安全启动器
├── nodal-spring-boot-starter-mybatis/      # MyBatis启动器
├── nodal-spring-boot-starter-redis/        # Redis启动器
├── nodal-spring-boot-starter-mq/           # 消息队列启动器
├── nodal-spring-boot-starter-job/          # 定时任务启动器
├── nodal-spring-boot-starter-excel/        # Excel处理启动器
├── nodal-spring-boot-starter-monitor/      # 监控启动器
├── nodal-spring-boot-starter-protection/   # 保护启动器
├── nodal-spring-boot-starter-websocket/    # WebSocket启动器
├── nodal-spring-boot-starter-test/         # 测试启动器
├── nodal-spring-boot-starter-biz-ip/       # IP业务启动器
├── nodal-spring-boot-starter-biz-tenant/   # 多租户业务启动器
└── nodal-spring-boot-starter-biz-data-permission/ # 数据权限业务启动器
```

### 业务模块结构 (以system模块为例)
```
nodal-module-system/
├── nodal-module-system-api/     # API接口定义
│   └── src/main/java/
│       └── com/nodal/module/system/
│           ├── api/             # 对外API接口
│           ├── dto/             # 数据传输对象
│           └── enums/           # 枚举定义
├── nodal-module-system-biz/     # 业务逻辑实现
│   └── src/main/java/
│       └── com/nodal/module/system/
│           ├── controller/      # 控制器层
│           │   ├── admin/       # 管理端控制器
│           │   └── app/         # 应用端控制器
│           ├── service/         # 服务层
│           │   └── impl/        # 服务实现
│           ├── dal/             # 数据访问层
│           │   ├── dataobject/  # 数据对象
│           │   ├── mysql/       # MySQL数据访问
│           │   └── redis/       # Redis数据访问
│           ├── convert/         # 对象转换器
│           └── framework/       # 框架扩展
└── pom.xml
```

## 架构设计模式

### 1. 分层架构
- **Controller层** - 接收HTTP请求，参数校验
- **Service层** - 业务逻辑处理
- **DAL层** - 数据访问抽象
- **DataObject层** - 数据库实体对象

### 2. 模块化设计
- **API模块** - 接口定义和DTO
- **BIZ模块** - 业务逻辑实现
- **Framework模块** - 框架能力封装

### 3. 依赖管理
- **统一版本管理** - nodal-dependencies统一管理所有依赖版本
- **按需引入** - 模块化设计支持按需引入功能模块

## 代码规范

### 1. 包命名规范
```
com.nodal.module.{模块名}.{层级}
├── controller/     # 控制器
├── service/        # 服务层
├── dal/           # 数据访问层
├── convert/       # 转换器
├── api/           # API接口
├── dto/           # 数据传输对象
└── enums/         # 枚举
```

### 2. 类命名规范
- **Controller** - {业务}Controller
- **Service** - {业务}Service / {业务}ServiceImpl
- **Mapper** - {业务}Mapper
- **DO** - {业务}DO
- **DTO** - {业务}{场景}ReqVO / {业务}{场景}RespVO

### 3. 方法命名规范
- **查询** - get{业务} / list{业务} / page{业务}
- **新增** - create{业务}
- **修改** - update{业务}
- **删除** - delete{业务}

## 配置管理

### 1. 环境配置
- **application.yaml** - 主配置文件
- **application-{env}.yaml** - 环境特定配置

### 2. 多数据库支持
- **MySQL** - 主要数据库
- **达梦、人大金仓、OpenGauss** - 国产数据库支持

### 3. 多租户配置
- **租户隔离** - 数据和权限隔离
- **忽略表配置** - 系统级表不参与租户隔离

## 扩展性设计

1. **插件化架构** - Spring Boot Starter模式
2. **模块化设计** - 业务模块可独立开发部署
3. **配置外部化** - 支持配置中心集成
4. **监控集成** - 内置监控和链路追踪
5. **多数据源** - 支持读写分离和分库分表
