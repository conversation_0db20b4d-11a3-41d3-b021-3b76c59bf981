# 项目总览文档

## 项目简介

本项目是一个基于前后端分离架构的企业级管理系统，采用现代化的技术栈构建，支持多租户、多数据库、工作流等企业级功能。

## 项目结构

```
jishu/
├── jishu-admin/          # 前端管理后台 (Vue 3 + TypeScript)
├── jishu-api/           # 后端API服务 (Spring Boot 3 + Java 17)
├── jishu-doc/           # 项目文档
└── docs/                # 项目分析文档
    ├── 技术选型分析.md
    ├── 代码结构分析.md
    ├── 数据库设计规范.md
    └── 项目总览.md
```

## 技术架构

### 前端技术栈
- **核心框架**: Vue 3.4.21 + TypeScript 5.3.3
- **构建工具**: Vite 5.1.4
- **UI组件**: Element Plus 2.7.0
- **状态管理**: Pinia 2.1.7
- **路由管理**: Vue Router 4.3.0
- **样式方案**: UnoCSS 0.58.5 + Sass
- **特色功能**: BPMN工作流设计器、富文本编辑器、图表可视化

### 后端技术栈
- **核心框架**: Spring Boot 3.3.1 + Java 17
- **数据访问**: MyBatis Plus 3.5.7 + Druid连接池
- **缓存方案**: Redis + Redisson
- **消息队列**: RocketMQ
- **工作流引擎**: Flowable 7.0.1
- **安全框架**: Spring Security + JWT
- **文档工具**: SpringDoc + Knife4j
- **监控方案**: Spring Boot Admin + SkyWalking

## 核心功能模块

### 1. 系统管理模块 (system)
- **用户管理**: 用户CRUD、角色分配、部门管理
- **权限管理**: 基于RBAC的权限控制
- **菜单管理**: 动态菜单配置
- **字典管理**: 系统字典维护
- **参数配置**: 系统参数管理
- **操作日志**: 用户操作审计

### 2. 基础设施模块 (infra)
- **代码生成**: 基于数据库表的代码生成
- **文件管理**: 文件上传下载、多存储支持
- **定时任务**: Quartz定时任务管理
- **数据源管理**: 多数据源配置
- **API日志**: 接口访问日志记录
- **错误日志**: 系统异常日志管理

### 3. 工作流模块 (bpm)
- **流程设计**: 可视化流程设计器
- **流程部署**: 流程定义部署管理
- **任务管理**: 待办任务、已办任务
- **流程监控**: 流程实例监控

### 4. 扩展业务模块
- **支付模块** (pay): 支付渠道、支付订单管理
- **商城模块** (mall): 商品、订单、库存管理
- **会员模块** (member): 会员信息、积分管理
- **客户关系管理** (crm): 客户、商机、合同管理
- **企业资源规划** (erp): 采购、销售、库存管理
- **微信公众号** (mp): 公众号管理、消息推送
- **AI模块** (ai): AI对话、图像生成等功能

## 架构特点

### 1. 模块化设计
- **前端模块化**: 按业务功能组织API和页面
- **后端模块化**: Maven多模块项目，支持按需引入
- **插件化架构**: Spring Boot Starter模式

### 2. 多租户支持
- **数据隔离**: 基于tenant_id的数据隔离
- **权限隔离**: 租户级别的权限控制
- **配置隔离**: 租户独立配置管理

### 3. 多数据库支持
- **主流数据库**: MySQL、PostgreSQL、Oracle、SQL Server
- **国产数据库**: 达梦、人大金仓、OpenGauss
- **自动适配**: 智能识别数据库类型

### 4. 高可用设计
- **缓存机制**: Redis缓存提升性能
- **连接池**: Druid连接池优化
- **监控体系**: 完整的监控和链路追踪
- **容错机制**: 异常处理和降级策略

## 开发规范

### 1. 代码规范
- **命名规范**: 统一的包名、类名、方法名规范
- **注释规范**: 完整的类和方法注释
- **代码格式**: ESLint + Prettier统一代码风格

### 2. 数据库规范
- **表命名**: 模块前缀 + 业务对象
- **字段规范**: 统一的字段类型和命名
- **索引设计**: 合理的索引策略
- **审计字段**: 统一的创建、更新、删除字段

### 3. API设计规范
- **RESTful**: 遵循REST API设计原则
- **统一响应**: 标准的响应格式
- **参数校验**: 完整的参数验证
- **错误处理**: 统一的错误码和消息

## 部署架构

### 1. 开发环境
- **前端**: Vite开发服务器
- **后端**: Spring Boot内嵌Tomcat
- **数据库**: 本地MySQL
- **缓存**: 本地Redis

### 2. 生产环境
- **容器化**: Docker容器部署
- **负载均衡**: Nginx反向代理
- **数据库**: MySQL主从集群
- **缓存**: Redis集群
- **监控**: Prometheus + Grafana

## 安全机制

### 1. 认证授权
- **JWT认证**: 无状态token认证
- **RBAC权限**: 基于角色的访问控制
- **数据权限**: 细粒度数据权限控制

### 2. 数据安全
- **数据加密**: 敏感数据加密存储
- **数据脱敏**: 敏感信息脱敏显示
- **审计日志**: 完整的操作审计

### 3. 接口安全
- **参数校验**: 严格的参数验证
- **SQL注入防护**: MyBatis预编译
- **XSS防护**: 输入输出过滤

## 性能优化

### 1. 前端优化
- **代码分割**: 路由级别的代码分割
- **资源压缩**: Gzip压缩和资源优化
- **缓存策略**: 浏览器缓存和CDN

### 2. 后端优化
- **数据库优化**: 索引优化和查询优化
- **缓存策略**: 多级缓存机制
- **连接池**: 数据库连接池优化

## 扩展性设计

### 1. 水平扩展
- **微服务就绪**: 模块化设计支持微服务拆分
- **数据库分片**: 支持分库分表
- **缓存集群**: Redis集群支持

### 2. 功能扩展
- **插件机制**: 支持功能插件扩展
- **配置外部化**: 支持配置中心
- **事件驱动**: 基于事件的解耦设计

## 文档体系

1. **[技术选型分析](./技术选型分析.md)** - 详细的技术栈分析和选型理由
2. **[代码结构分析](./代码结构分析.md)** - 项目结构和架构设计分析
3. **[数据库设计规范](./数据库设计规范.md)** - 数据库设计规范和最佳实践
4. **[后续开发规则](./后续开发规则.md)** - 基于jishu_前缀的新业务开发规范

## 快速开始

### 1. 环境要求
- **Node.js**: 16.0+
- **Java**: 17+
- **MySQL**: 8.0+
- **Redis**: 6.0+

### 2. 启动步骤
```bash
# 后端启动
cd jishu-api
mvn clean install
mvn spring-boot:run

# 前端启动
cd jishu-admin
pnpm install
pnpm dev
```

### 3. 访问地址
- **前端地址**: http://localhost:3000
- **后端地址**: http://localhost:8080
- **API文档**: http://localhost:8080/swagger-ui

## 总结

本项目采用现代化的技术栈和架构设计，具备企业级应用所需的完整功能和非功能特性。通过模块化设计、多租户支持、多数据库兼容等特性，为企业提供了一个可扩展、高性能、安全可靠的管理系统解决方案。
