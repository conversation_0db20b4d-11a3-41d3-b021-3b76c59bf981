# 角色权限配置文档

## 系统角色体系

### 角色定义
系统基于业务场景定义四个核心角色，每个角色对应不同的业务职责和权限范围。

| 角色代码 | 角色名称 | 业务职责 | 权限范围 |
|---------|---------|---------|---------|
| `jishu_hr` | 人事管理员 | 员工管理、考勤管理、薪资管理 | 人事模块全部权限 |
| `jishu_finance` | 财务管理员 | 费用审核、报销管理、财务报表 | 财务模块全部权限 + 薪资查询权限 |
| `jishu_dispatch` | 调度管理员 | 任务调度、车辆管理、路线规划 | 调度模块全部权限 |
| `jishu_driver` | 司机 | 任务执行、状态上报、费用提交 | 司机模块权限 + 只读权限 |

## 权限矩阵

### 人事管理员权限 (jishu_hr)

#### 员工管理
- ✅ `jishu:hr:employee:query` - 员工查询
- ✅ `jishu:hr:employee:create` - 员工创建
- ✅ `jishu:hr:employee:update` - 员工更新
- ✅ `jishu:hr:employee:delete` - 员工删除
- ✅ `jishu:hr:employee:export` - 员工导出

#### 考勤管理
- ✅ `jishu:hr:attendance:query` - 考勤查询
- ✅ `jishu:hr:attendance:manage` - 考勤管理
- ✅ `jishu:hr:attendance:export` - 考勤导出
- ✅ `jishu:hr:attendance:statistics` - 考勤统计

#### 薪资管理
- ✅ `jishu:hr:salary:query` - 薪资查询
- ✅ `jishu:hr:salary:manage` - 薪资管理
- ✅ `jishu:hr:salary:calculate` - 薪资计算
- ✅ `jishu:hr:salary:export` - 薪资导出

### 财务管理员权限 (jishu_finance)

#### 费用管理
- ✅ `jishu:finance:expense:query` - 费用查询
- ✅ `jishu:finance:expense:audit` - 费用审核
- ✅ `jishu:finance:expense:export` - 费用导出
- ✅ `jishu:finance:expense:statistics` - 费用统计

#### 报销管理
- ✅ `jishu:finance:reimburse:query` - 报销查询
- ✅ `jishu:finance:reimburse:audit` - 报销审核
- ✅ `jishu:finance:reimburse:pay` - 报销支付
- ✅ `jishu:finance:reimburse:export` - 报销导出

#### 财务报表
- ✅ `jishu:finance:report:query` - 报表查询
- ✅ `jishu:finance:report:export` - 报表导出
- ✅ `jishu:finance:report:statistics` - 财务统计

#### 薪资相关（跨模块权限）
- ✅ `jishu:hr:salary:query` - 薪资查询（只读）
- ✅ `jishu:hr:salary:pay` - 薪资发放

### 调度管理员权限 (jishu_dispatch)

#### 任务管理
- ✅ `jishu:dispatch:task:query` - 任务查询
- ✅ `jishu:dispatch:task:create` - 任务创建
- ✅ `jishu:dispatch:task:update` - 任务更新
- ✅ `jishu:dispatch:task:delete` - 任务删除
- ✅ `jishu:dispatch:task:assign` - 任务分配
- ✅ `jishu:dispatch:task:cancel` - 任务取消
- ✅ `jishu:dispatch:task:export` - 任务导出

#### 车辆管理
- ✅ `jishu:dispatch:vehicle:query` - 车辆查询
- ✅ `jishu:dispatch:vehicle:create` - 车辆创建
- ✅ `jishu:dispatch:vehicle:update` - 车辆更新
- ✅ `jishu:dispatch:vehicle:delete` - 车辆删除
- ✅ `jishu:dispatch:vehicle:assign` - 车辆分配
- ✅ `jishu:dispatch:vehicle:maintenance` - 车辆维护

#### 路线管理
- ✅ `jishu:dispatch:route:query` - 路线查询
- ✅ `jishu:dispatch:route:plan` - 路线规划
- ✅ `jishu:dispatch:route:optimize` - 路线优化

### 司机权限 (jishu_driver)

#### 任务相关
- ✅ `jishu:driver:task:query` - 我的任务查询
- ✅ `jishu:driver:task:accept` - 任务接受
- ✅ `jishu:driver:task:reject` - 任务拒绝
- ✅ `jishu:driver:task:report` - 任务状态上报
- ✅ `jishu:driver:task:complete` - 任务完成

#### 车辆相关
- ✅ `jishu:driver:vehicle:query` - 车辆信息查询（只读）
- ✅ `jishu:driver:vehicle:status` - 车辆状态上报

#### 位置相关
- ✅ `jishu:driver:location:update` - 位置更新
- ✅ `jishu:driver:location:track` - 轨迹记录

#### 费用相关
- ✅ `jishu:driver:expense:submit` - 费用提交
- ✅ `jishu:driver:expense:query` - 我的费用查询

## 数据权限规则

### 数据隔离级别

#### 人事管理员
- **员工数据**: 可查看所有员工信息
- **考勤数据**: 可查看所有员工考勤记录
- **薪资数据**: 可查看和管理所有员工薪资

#### 财务管理员
- **费用数据**: 可查看所有费用申请和报销记录
- **财务数据**: 可查看所有财务相关数据
- **薪资数据**: 可查看薪资数据，可执行薪资发放

#### 调度管理员
- **任务数据**: 可查看和管理所有任务
- **车辆数据**: 可查看和管理所有车辆
- **司机数据**: 可查看司机基本信息（不包含薪资）

#### 司机
- **任务数据**: 只能查看分配给自己的任务
- **车辆数据**: 只能查看分配给自己的车辆信息
- **费用数据**: 只能查看和提交自己的费用记录
- **个人数据**: 只能查看自己的基本信息

### 数据权限实现

#### 基于用户ID的数据过滤
```java
// 司机只能查看自己的任务
@DataPermission(
    clazz = JishuDispatchTaskDO.class,
    column = "driver_id",
    condition = DataPermissionCondition.EQUAL
)
public List<JishuDispatchTaskDO> getDriverTasks(Long driverId);

// 司机只能查看自己的费用记录
@DataPermission(
    clazz = JishuFinanceExpenseDO.class,
    column = "employee_id",
    condition = DataPermissionCondition.EQUAL
)
public List<JishuFinanceExpenseDO> getDriverExpenses(Long employeeId);
```

#### 基于角色的数据访问
```java
// 根据角色判断数据访问权限
@PreAuthorize("hasRole('jishu_hr') or hasRole('jishu_finance')")
public List<JishuHrEmployeeDO> getAllEmployees();

@PreAuthorize("hasRole('jishu_dispatch')")
public List<JishuDispatchTaskDO> getAllTasks();

@PreAuthorize("hasRole('jishu_driver') and #driverId == authentication.principal.id")
public List<JishuDispatchTaskDO> getMyTasks(Long driverId);
```

## 菜单权限配置

### 菜单层级结构
```
技术管理 (/jishu)
├── 人事管理 (/jishu/hr) [jishu_hr]
│   ├── 员工管理 [jishu:hr:employee:query]
│   ├── 考勤管理 [jishu:hr:attendance:query]
│   └── 薪资管理 [jishu:hr:salary:query]
├── 财务管理 (/jishu/finance) [jishu_finance]
│   ├── 费用管理 [jishu:finance:expense:query]
│   ├── 报销管理 [jishu:finance:reimburse:query]
│   └── 财务报表 [jishu:finance:report:query]
├── 调度管理 (/jishu/dispatch) [jishu_dispatch]
│   ├── 任务管理 [jishu:dispatch:task:query]
│   ├── 车辆管理 [jishu:dispatch:vehicle:query]
│   └── 路线管理 [jishu:dispatch:route:query]
└── 司机工作台 (/jishu/driver) [jishu_driver]
    ├── 我的任务 [jishu:driver:task:query]
    ├── 车辆信息 [jishu:driver:vehicle:query]
    └── 费用提交 [jishu:driver:expense:submit]
```

### 按钮权限配置
```html
<!-- 人事管理员按钮权限 -->
<el-button v-hasPermi="['jishu:hr:employee:create']">新增员工</el-button>
<el-button v-hasPermi="['jishu:hr:employee:update']">编辑</el-button>
<el-button v-hasPermi="['jishu:hr:employee:delete']">删除</el-button>

<!-- 财务管理员按钮权限 -->
<el-button v-hasPermi="['jishu:finance:expense:audit']">审核</el-button>
<el-button v-hasPermi="['jishu:finance:reimburse:pay']">支付</el-button>

<!-- 调度管理员按钮权限 -->
<el-button v-hasPermi="['jishu:dispatch:task:create']">创建任务</el-button>
<el-button v-hasPermi="['jishu:dispatch:task:assign']">分配任务</el-button>

<!-- 司机按钮权限 -->
<el-button v-hasPermi="['jishu:driver:task:accept']">接受任务</el-button>
<el-button v-hasPermi="['jishu:driver:expense:submit']">提交费用</el-button>
```

## 权限验证规范

### 后端权限验证
```java
// 方法级权限验证
@PreAuthorize("hasAuthority('jishu:hr:employee:create')")
public Long createEmployee(JishuHrEmployeeCreateReqVO reqVO);

@PreAuthorize("hasAuthority('jishu:finance:expense:audit')")
public void auditExpense(Long expenseId, Boolean approved, String remark);

@PreAuthorize("hasAuthority('jishu:dispatch:task:assign')")
public void assignTask(Long taskId, Long driverId, Long vehicleId);

// 数据权限验证
@PreAuthorize("hasAuthority('jishu:driver:task:query') and #driverId == authentication.principal.id")
public List<JishuDispatchTaskDO> getMyTasks(Long driverId);
```

### 前端权限验证
```typescript
// 路由权限验证
{
  path: '/jishu/hr',
  component: Layout,
  meta: { 
    title: '人事管理',
    roles: ['jishu_hr']
  }
}

// 组件权限验证
import { hasPermi } from '@/utils/permission'

// 检查按钮权限
const hasCreatePermission = hasPermi(['jishu:hr:employee:create'])

// 检查角色权限
const isHrManager = hasRole(['jishu_hr'])
```

## 权限配置脚本

### 完整权限配置SQL
```sql
-- 创建角色
INSERT INTO system_role (name, code, sort, status, type, remark) VALUES 
('人事管理员', 'jishu_hr', 1, 0, 2, '负责员工管理、考勤管理等人事相关业务'),
('财务管理员', 'jishu_finance', 2, 0, 2, '负责财务管理、费用审核、报销等财务相关业务'),
('调度管理员', 'jishu_dispatch', 3, 0, 2, '负责任务调度、车辆调度、路线规划等调度相关业务'),
('司机', 'jishu_driver', 4, 0, 2, '负责车辆驾驶、任务执行、状态上报等司机相关业务');

-- 分配角色权限（详细的菜单权限分配见后续开发规则文档）
```

---

*本文档定义了系统的完整角色权限体系，开发时请严格按照权限矩阵进行功能实现和权限控制。*
