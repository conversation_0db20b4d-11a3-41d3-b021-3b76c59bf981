package com.nodal.module.jishu.enums;

import com.nodal.framework.common.exception.ErrorCode;

/**
 * Jishu 错误码枚举类
 *
 * jishu 系统，使用 1-003-000-000 段
 */
public interface ErrorCodeConstants {

    // ========== 人事管理模块 1-003-001-000 ==========
    ErrorCode HR_EMPLOYEE_NOT_EXISTS = new ErrorCode(1_003_001_000, "员工不存在");
    ErrorCode HR_EMPLOYEE_NO_DUPLICATE = new ErrorCode(1_003_001_001, "员工编号已存在");
    ErrorCode HR_ATTENDANCE_NOT_EXISTS = new ErrorCode(1_003_001_002, "考勤记录不存在");

    // ========== 财务管理模块 1-003-002-000 ==========
    ErrorCode FINANCE_EXPENSE_NOT_EXISTS = new ErrorCode(1_003_002_000, "费用申请不存在");
    ErrorCode FINANCE_EXPENSE_NO_DUPLICATE = new ErrorCode(1_003_002_001, "费用单号已存在");
    ErrorCode FINANCE_EXPENSE_AUDITED = new ErrorCode(1_003_002_002, "费用申请已审核，无法修改");

    // ========== 调度管理模块 1-003-003-000 ==========
    ErrorCode DISPATCH_TASK_NOT_EXISTS = new ErrorCode(1_003_003_000, "任务不存在");
    ErrorCode DISPATCH_TASK_NO_DUPLICATE = new ErrorCode(1_003_003_001, "任务编号已存在");
    ErrorCode DISPATCH_VEHICLE_NOT_EXISTS = new ErrorCode(1_003_003_002, "车辆不存在");
    ErrorCode DISPATCH_VEHICLE_NO_DUPLICATE = new ErrorCode(1_003_003_003, "车牌号已存在");
    ErrorCode DISPATCH_VEHICLE_IN_USE = new ErrorCode(1_003_003_004, "车辆使用中，无法删除");

    // ========== 司机业务模块 1-003-004-000 ==========
    ErrorCode DRIVER_TASK_RECORD_NOT_EXISTS = new ErrorCode(1_003_004_000, "司机任务记录不存在");
    ErrorCode DRIVER_TASK_NOT_ASSIGNED = new ErrorCode(1_003_004_001, "任务未分配给当前司机");
    ErrorCode DRIVER_TASK_STATUS_ERROR = new ErrorCode(1_003_004_002, "任务状态错误，无法执行此操作");

}
