<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.nodal</groupId>
        <artifactId>nodal-module-jishu</artifactId>
        <version>${revision}</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>nodal-module-jishu-biz</artifactId>
    <packaging>jar</packaging>

    <name>${project.artifactId}</name>
    <description>
        jishu 模块下，我们放技术平台业务，支撑四大角色的核心业务。
        包括：人事管理、财务管理、调度管理、司机业务等等
    </description>

    <dependencies>
        <dependency>
            <groupId>com.nodal</groupId>
            <artifactId>nodal-module-jishu-api</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>com.nodal</groupId>
            <artifactId>nodal-module-system-api</artifactId>
            <version>${revision}</version>
        </dependency>
        <dependency>
            <groupId>com.nodal</groupId>
            <artifactId>nodal-module-infra-api</artifactId>
            <version>${revision}</version>
        </dependency>

        <!-- 业务组件 -->
        <dependency>
            <groupId>com.nodal</groupId>
            <artifactId>nodal-spring-boot-starter-biz-data-permission</artifactId>
        </dependency>
        <dependency>
            <groupId>com.nodal</groupId>
            <artifactId>nodal-spring-boot-starter-biz-tenant</artifactId>
        </dependency>

        <!-- Web 相关 -->
        <dependency>
            <groupId>com.nodal</groupId>
            <artifactId>nodal-spring-boot-starter-security</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-validation</artifactId>
        </dependency>

        <!-- DB 相关 -->
        <dependency>
            <groupId>com.nodal</groupId>
            <artifactId>nodal-spring-boot-starter-mybatis</artifactId>
        </dependency>

        <dependency>
            <groupId>com.nodal</groupId>
            <artifactId>nodal-spring-boot-starter-redis</artifactId>
        </dependency>

        <!-- Test 测试相关 -->
        <dependency>
            <groupId>com.nodal</groupId>
            <artifactId>nodal-spring-boot-starter-test</artifactId>
            <scope>test</scope>
        </dependency>

        <!-- 工具类相关 -->
        <dependency>
            <groupId>com.nodal</groupId>
            <artifactId>nodal-spring-boot-starter-excel</artifactId>
        </dependency>
    </dependencies>

</project>
